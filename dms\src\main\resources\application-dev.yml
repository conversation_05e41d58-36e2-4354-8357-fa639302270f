eureka:
  instance:
    preferIpAddress: true
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone:  http://localhost:8070/eureka/
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    gateway:
      enabled: true
  info:
    env:
      enabled: true

info:
  app:
    name: "DMS"
    description: "HRDC Document Management System Application"
    version: "1.0.0"

app:
  logging:
    level: ERROR

server:
  contextPath: /hrdc
  port: 8092

spring:
  application:
    name: DMS
    profiles:
    active: local
  jackson:
    serialization:
      INDENT_OUTPUT: true
  datasource:
    url: ************************************
    username: postgres
    password: root
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: HikariCP
      idle-timeout: 30000
      max-lifetime: 60000
      connection-timeout: 30000
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  data:
    jpa:
      repositories:
        enabled: true
  liquibase:
    enabled: ${SPRING_LIQUIBASE_ENABLED:true}
    drop-first: ${SPRING_LIQUIBASE_DROP_FIRST:false}
    change-log: classpath:/db/changelog/db.changelog-master.xml
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://hrdcdev.weblogic.co.bw/keycloak/realms/hrdc
          jwk-set-uri: https://hrdcdev.weblogic.co.bw/keycloak/realms/hrdc/protocol/openid-connect/certs

logging:
  level:
    bw.co.farmconnecta: ${app.logging.level}
    com.brahalla: ${app.logging.level}
    org.springframework: ${app.logging.level}
    org.hibernate: ${app.logging.level}

error:
  whitelabel:
    enabled: true

security:
  token:
    header: X-Auth-Token
    secret: ssssshhhhhhhhh!
    expiration: 86400000

farmconnector:
  test:
    data:
      enable: true

hibernate:
  format_sql: false
  dialect: org.hibernate.dialect.PostgreSQLDialect


# Paperless-ngx API configuration
# paperless:
#   api:
#     url: "http://**************:8000"
#     token: "7e7cac2a2845e7482b1359e99daba7e855cc612d"
#     auth:
#       username: "paperless"
#       password: "paperless@123"

paperless:
  api:
    url: "http://localhost:8000"
    token: "1bfca2f81fb352f91a55ed3eac759e3ca46ab777"
    auth:
      username: "admin"
      password: "admin"
  # upload:
  #   max:
  #     attempts: 60  # Increased for better reliability
    delay:
      seconds: 3    # Increased for better reliability
